import { DefaultBodyType, http, HttpResponse, StrictRequest } from "msw";
import { setupServer } from "msw/node";
import {
  afterAll,
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  it,
} from "vitest";
import { createApp } from "./app";
import { compress, decompressBody } from "./compression";
import { FakeKV } from "./test/fake-kv";
import posthogIdentifyRequest from "./test/fixtures/posthog-identify-request.json";
import posthogIngestRequest from "./test/fixtures/posthog-ingest-request.json";

const requests: StrictRequest<DefaultBodyType>[] = [];

const server = setupServer(
  http.all("https://us.i.posthog.com/*", ({ request }) => {
    requests.push(request);
    return HttpResponse.text("proxied response from us.i.posthog.com", {
      status: 200,
    });
  }),
  http.all("https://us-assets.i.posthog.com/*", ({ request }) => {
    requests.push(request);
    return HttpResponse.text("proxied response from us-assets.i.posthog.com", {
      status: 200,
    });
  })
);

beforeAll(() => server.listen());
beforeEach(() => (requests.length = 0));
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

const app = await createApp({
  processEnv: {
    POSTHOG_KEY: "phc_posthog_key",
    POSTHOG_API_HOST: "us.i.posthog.com",
    POSTHOG_ASSETS_HOST: "us-assets.i.posthog.com",
  },
  saltKV: new FakeKV(),
});

describe("healthcheck", () => {
  it("responds OK", async () => {
    const req = new Request("http://localhost/healthcheck", {
      method: "GET",
    });

    const res = await app.fetch(req);
    expect(res.status).toBe(200);
    expect(await res.text()).toBe("OK");
  });
});

describe("posthogHandler", () => {
  it("proxies requests with $identify event", async () => {
    const req = new Request(posthogIdentifyRequest.url, {
      method: posthogIdentifyRequest.method,
      headers: posthogIdentifyRequest.headers,
      body: JSON.stringify(posthogIdentifyRequest.body),
    });

    const res = await app.fetch(req);
    expect(res.status).toBe(200);
    expect(await res.text()).toBe("proxied response from us.i.posthog.com");

    expect(requests.length).toBe(1);
    const request = requests[0];
    expect(request.method).toBe(posthogIdentifyRequest.method);
    expect(request.url).toBe(
      "https://us.i.posthog.com/e/?ip=1&_=1743989055088&ver=1.194.1"
    );
    expect(Object.fromEntries(request.headers.entries())).toEqual({
      ...posthogIdentifyRequest.headers,
      host: "us.i.posthog.com",
    });
    posthogIdentifyRequest.body.properties.$anon_distinct_id =
      expect.stringMatching(/^pcookieless_[A-Za-z0-9+/=]+$/);
    expect(await request.json()).toEqual(posthogIdentifyRequest.body);
  });

  it("proxies requests but doesn't process events if IP address is unknown", async () => {
    const headersWithoutXForwardedFor: Record<string, string> = {
      ...posthogIngestRequest.headers,
    };
    delete headersWithoutXForwardedFor["x-forwarded-for"];
    const req = new Request(posthogIngestRequest.url, {
      method: posthogIngestRequest.method,
      headers: headersWithoutXForwardedFor,
      body: JSON.stringify(posthogIngestRequest.body),
    });

    const res = await app.fetch(req);
    expect(res.status).toBe(200);
    expect(await res.text()).toBe("proxied response from us.i.posthog.com");

    expect(requests.length).toBe(1);
    const request = requests[0];
    expect(request.method).toBe(posthogIngestRequest.method);
    expect(request.url).toBe(
      "https://us.i.posthog.com/i/v0/e/?ip=1&_=1743989055088&ver=1.194.1"
    );
    expect(Object.fromEntries(request.headers.entries())).toEqual({
      ...headersWithoutXForwardedFor,
      host: "us.i.posthog.com",
    });
    expect(await request.json()).toEqual(posthogIngestRequest.body);
  });

  it("proxies requests with event data (uncompressed)", async () => {
    const req = new Request(posthogIngestRequest.url, {
      method: posthogIngestRequest.method,
      headers: posthogIngestRequest.headers,
      body: JSON.stringify(posthogIngestRequest.body),
    });

    const res = await app.fetch(req);
    expect(res.status).toBe(200);
    expect(await res.text()).toBe("proxied response from us.i.posthog.com");

    expect(requests.length).toBe(1);
    const request = requests[0];
    expect(request.method).toBe(posthogIngestRequest.method);
    expect(request.url).toBe(
      "https://us.i.posthog.com/i/v0/e/?ip=1&_=1743989055088&ver=1.194.1"
    );
    expect(Object.fromEntries(request.headers.entries())).toEqual({
      ...posthogIngestRequest.headers,
      host: "us.i.posthog.com",
    });
    posthogIngestRequest.body[0].properties.distinct_id = expect.stringMatching(
      /^pcookieless_[A-Za-z0-9+/=]+$/
    );
    posthogIngestRequest.body[0].properties.$device_id = expect.stringMatching(
      /^pcookieless_[A-Za-z0-9+/=]+$/
    );
    posthogIngestRequest.body[0].properties.$session_id = expect.stringMatching(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/
    );
    expect(await request.json()).toEqual(posthogIngestRequest.body);
  });

  it("proxies requests with event data (gzipped)", async () => {
    const req = new Request(posthogIngestRequest.url + "&compression=gzip-js", {
      method: posthogIngestRequest.method,
      headers: posthogIngestRequest.headers,
      body: compress(posthogIngestRequest.body),
    });

    const res = await app.fetch(req);
    expect(res.status).toBe(200);
    expect(await res.text()).toBe("proxied response from us.i.posthog.com");

    expect(requests.length).toBe(1);
    const request = requests[0];
    expect(request.method).toBe(posthogIngestRequest.method);
    expect(request.url).toBe(
      "https://us.i.posthog.com/i/v0/e/?ip=1&_=1743989055088&ver=1.194.1&compression=gzip-js"
    );
    expect(Object.fromEntries(request.headers.entries())).toEqual({
      ...posthogIngestRequest.headers,
      host: "us.i.posthog.com",
    });
    posthogIngestRequest.body[0].properties.distinct_id = expect.stringMatching(
      /^pcookieless_[A-Za-z0-9+/=]+$/
    );
    posthogIngestRequest.body[0].properties.$device_id = expect.stringMatching(
      /^pcookieless_[A-Za-z0-9+/=]+$/
    );
    posthogIngestRequest.body[0].properties.$session_id = expect.stringMatching(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/
    );
    expect(await decompressBody(request)).toEqual(posthogIngestRequest.body);
  });

  it("proxies static assets like toolbar.js", async () => {
    const headers = {
      "sec-ch-ua":
        '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"macOS"',
      referer: "https://www.plasmic.app/",
      "referrer-policy": "strict-origin-when-cross-origin",
    };
    const req = new Request(
      `http://localhost/static/toolbar.js?v=1.194.1&t=1743986100000`,
      {
        method: "GET",
        headers: headers,
      }
    );

    const res = await app.fetch(req);
    expect(res.status).toBe(200);
    expect(await res.text()).toBe(
      "proxied response from us-assets.i.posthog.com"
    );

    expect(requests.length).toBe(1);
    const request = requests[0];
    expect(request.method).toBe("GET");
    expect(request.url).toBe(
      `https://us-assets.i.posthog.com/static/toolbar.js?v=1.194.1&t=1743986100000`
    );
    expect(Object.fromEntries(request.headers.entries())).toEqual({
      ...headers,
      host: "us-assets.i.posthog.com",
      "cf-connecting-ip": undefined,
    });
    expect(request.body).toBeNull();
  });

  it("responds 401 for requests with wrong token", async () => {
    posthogIdentifyRequest.body.properties.token = "phc_posthog_wrong_key";
    const req = new Request(posthogIdentifyRequest.url, {
      method: posthogIdentifyRequest.method,
      headers: posthogIdentifyRequest.headers,
      body: JSON.stringify(posthogIdentifyRequest.body),
    });

    const res = await app.fetch(req);
    expect(res.status).toBe(401);
    expect(await res.text()).toBe("Invalid token");

    expect(requests.length).toBe(0);
  });
});
